import {
  <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>,
  ChangeDetector<PERSON><PERSON>,
  <PERSON>mponent,
  ElementRef,
  HostListener,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild
} from "@angular/core";
import {PinchZoomComponent, PinchZoomModule} from "@meddv/ngx-pinch-zoom";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>} from "@angular/common";
import {AnnotationCreatorComponent} from "../annotation-creator/annotation-creator.component";
import {TimerComponent} from "../timer/timer.component";
import {TopbarComponent} from "../topbar/topbar.component";
import {DrawingService} from "../../services/drawing.service";
import {ImageRecoverService} from "../../services/image-recover.service";
import {LocalStorageService} from "../../services/local-storage.service";
import {AnnotationService} from "../../services/annotation.service";
import {ZoomControlService} from "../../services/zoom-control.service";
import {RectangleService} from "../../services/rectangle.service";
import {AudioPlayerComponent} from "../audio-player/audio-player.component";
import {AudioService} from "../../services/audio.service";
import {FinishTimeService} from "../../services/finish-time.service";
import {ClickstreamService} from "../../services/clickstream.service";
import {Observable} from "rxjs";

export interface RectangleHelper {
  confiance: number;
  frequence_debut: number;
  frequence_fin: number;
  temps_debut: number;
  temps_fin: number;
  type_bruiteur: string;

  trust ?: number;
  bruiteur ?: string;
}

export interface Rectangle {
  startX: number;
  startY: number;
  width: number;
  height: number;
  bruiteur: string;
  trust: number;
  userName: string | null;
  imageName: string | null;
  fLeft?: number;
  fRight?: number;
  tTop?: number;
  tBottom?: number;
  isPersisted?: boolean;
  // AI suggestion properties
  isAiSuggestion?: boolean;
  clickType?: string;
  suggestionType?: string;
}

export interface Annotation {
  bruiteur: string;
  trust: number;
}

@Component({
  selector: 'app-spectro',
  standalone: true,
  imports: [TopbarComponent,
    TimerComponent,
    AnnotationCreatorComponent,
    NgIf, PinchZoomModule, AudioPlayerComponent, NgStyle, NgForOf],
  templateUrl: './spectro.component.html',
  styleUrl: './spectro.component.css'
})

export class SpectroComponent implements AfterViewInit, OnInit, OnDestroy {
  @ViewChild('imageCanvas', { static: true }) canvasRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('pinchZoom') pinchZoomComponent!: PinchZoomComponent;
  @ViewChild('audioPlayer', { static: true }) audioRef!: ElementRef<HTMLAudioElement>;

  userName: string | null;
  spectroName: string | null;
  maxTime: number = 0;
  timeString: Observable<string>;


  constructor(private elementRef: ElementRef, private drawingService: DrawingService,
              private spectroRecover: ImageRecoverService, private localStorage: LocalStorageService,
              private annotationService: AnnotationService,
              protected zoomService: ZoomControlService, private rectangleService: RectangleService,
              private audioService : AudioService, private cdr: ChangeDetectorRef, private finishTimeService: FinishTimeService,
              private clickstreamService: ClickstreamService) {

    this.userName = this.localStorage.getItem("uniqueId");
    this.spectroName = this.localStorage.getItem("spectroName");
    this.timeString = this.finishTimeService.getFinishTime(this.spectroName);

  }

  zoomLevel: number = 1;
  private intervalId: any;

  cursorX: number = 0;
  cursorY: number = 0;

  frequency: number = 0;
  time : number = 0;

  Maxfrequency = 22050;
  // timeMinutes = 20;
  // maxTime = this.timeMinutes * 60000;


  private canvas!: HTMLCanvasElement;
  private ctx!: CanvasRenderingContext2D;
  private image!: HTMLImageElement;

  private isDrawing = false;
  private startX = 0;
  private startY = 0;

  rectangles: Rectangle[] = [];
  currentRectangle: Rectangle | null = null;


  middleClickPositions: { x: number; y: number }[] = [];
  isAudioPlayerVisible = false;
  audio = new Audio();
  startTime: number = 0;
  endTime: number = 0;

  rectangleHelperTest : RectangleHelper[] = [
    {
      frequence_debut: 162.5,
      frequence_fin: 337.5,
      temps_debut: 1055048.0498866213,
      temps_fin: 1065048.0272108843,
      confiance: 1,
      type_bruiteur: 'Brouilleur'
    },
    {
      frequence_debut : 175,
      frequence_fin : 325,
      temps_debut: 1033372.6303854876,
      temps_fin: 1043372.6077097505,
      type_bruiteur: 'Hélice',
      confiance: 0.5
    }
  ]

  ngAfterViewInit() {
    document.body.style.backgroundColor = 'black';
    this.initializeCanvas();

    // console.log('calcul des rectangles :', this.convertRectangleHelpers(this.rectangleHelperTest, false));
  }

  ngOnInit(): void {
    // this.initializeCanvas(); // peut etre pas bon
    this.getZoomLevel();
    this.setupFullScreenListener();
    this.finishTimeService.getTimeALLMission(this.spectroName).subscribe(
      timeString => {
        this.maxTime = this.finishTimeService.timeStringToMilliseconds(timeString);
      }
    )
  }

  ngOnDestroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    // Remove full screen event listeners
    document.removeEventListener('fullscreenchange', this.onFullScreenChange);
    document.removeEventListener('webkitfullscreenchange', this.onFullScreenChange);
    document.removeEventListener('mozfullscreenchange', this.onFullScreenChange);
  }

  setupFullScreenListener(): void {
    // Add event listeners for full screen changes
    document.addEventListener('fullscreenchange', this.onFullScreenChange);
    document.addEventListener('webkitfullscreenchange', this.onFullScreenChange);
    document.addEventListener('mozfullscreenchange', this.onFullScreenChange);
  }

  onFullScreenChange = (): void => {
    // Small delay to ensure the browser has finished the full screen transition
    setTimeout(() => {
      if (this.canvas && this.image && this.image.complete) {
        this.resizeCanvas();
        this.redrawCanvas();
      }
    }, 100);
  }

  addSpectroLocalService() {
    if (this.spectroName) {
      // if the name is stored call LocalStorage
      this.spectroRecover.getImageFromName(this.spectroName).subscribe(
        (response) => {
          const mimeType = 'image/png';
          this.image.src = `data:${mimeType};base64,${response.image_data}`;
        },
        (error) => {
          console.log('Error when retrieving image by name:', error);
        }
      )
    }
  }


  initializeCanvas(): void {
    this.canvas = this.canvasRef.nativeElement
    this.ctx = this.canvas.getContext('2d') as CanvasRenderingContext2D;

    this.image = new Image();
    this.addSpectroLocalService()

    this.image.onload = () => {
      this.resizeCanvas();
      this.redrawCanvas();

      console.log('Test function convertRectangleHelpers :', this.convertRectangleHelpers(this.rectangleHelperTest, true));

      // Recover rectangle and display
      const nameUser = this.localStorage.getItem("uniqueId");
      const nameSpectro = this.localStorage.getItem("spectroName");

      this.annotationService.getRectangles(nameUser, nameSpectro).subscribe(rectangles => {
        const rect = this.convertRectangleHelpers(rectangles, true)
        console.log('Je rentre dans la baaille ? ')
        this.rectangleService.setRectangles(rect);
        this.redrawCanvas();
      })
    };

    // console.log("Initialized canvas add rectangles : ", this.rectangles.length);
  }

  convertRectangleHelpers(rectangleHelps: RectangleHelper[], persisted: boolean, isAiSuggestion: boolean = false): Rectangle[] {
    // console.log("Converting Ai Rectangle on Ai mode Toogle : ", rectangleHelps);
    return rectangleHelps.map(iRect => ({
      startX: this.frequencyToPixel(iRect.frequence_debut, this.canvas.width, this.Maxfrequency),
      // startX: (iRect.frequence_debut / this.Maxfrequency) * this.canvas.width,
      startY: this.timeToPixel(iRect.temps_fin, this.canvas.height, this.maxTime),
      width: this.frequencyToPixel(iRect.frequence_fin - iRect.frequence_debut, this.canvas.width, this.Maxfrequency),
      height: this.canvas.height * ((iRect.temps_fin - iRect.temps_debut) / this.maxTime),

      // startX: (iRect.frequence_debut / this.Maxfrequency) * this.canvas.width,
      // startY: this.canvas.height - ((iRect.temps_fin / this.maxTime) * this.canvas.height),
      // width: ((iRect.frequence_fin - iRect.frequence_debut) / this.Maxfrequency) * this.canvas.width,
      // height: ((iRect.temps_fin - iRect.temps_debut) / this.maxTime) * this.canvas.height,

      bruiteur: iRect.type_bruiteur ?? iRect.bruiteur ,
      trust: iRect.confiance ?? iRect.trust,
      userName: this.userName,
      imageName: this.spectroName,
      fLeft: iRect.frequence_debut,
      fRight: iRect.frequence_fin,
      tBottom: iRect.temps_debut,
      tTop: iRect.temps_fin,
      isPersisted: persisted,
      // Add AI suggestion markers
      isAiSuggestion: isAiSuggestion,
      clickType: isAiSuggestion ? 'ai_rectangle_interaction' : undefined,
      suggestionType: isAiSuggestion ? 'ai_rectangle' : undefined
    }));

  }

  getZoomLevel() {
    this.intervalId = setInterval(() => {
      if (this.pinchZoomComponent) {
        this.zoomLevel = this.pinchZoomComponent.scale;
      }
    }, 1000)
  }

  resizeCanvas(): void {
    if (!this.canvas || !this.image || !this.ctx) return;

    // Check if we're in full screen mode
    const isFullScreen = !!(document.fullscreenElement ||
                           (document as any).webkitFullscreenElement ||
                           (document as any).mozFullScreenElement);

    let availableHeight: number;

    if (isFullScreen) {
      // In full screen mode, use the entire screen height
      availableHeight = window.innerHeight;
      console.log('Full screen mode detected');
    } else {
      // Normal mode: subtract topbar and timer heights
      const topbarElement = this.elementRef.nativeElement.querySelector('app-topbar');
      const timerElement = this.elementRef.nativeElement.querySelector('app-timer');

      const topbarHeight = topbarElement ? topbarElement.offsetHeight : 0;
      const timerHeight = timerElement ? timerElement.offsetHeight : 0;

      availableHeight = window.innerHeight - topbarHeight - timerHeight;
    }

    // Set canvas dimensions
    this.canvas.width = this.image.width;
    this.canvas.height = Math.min(this.image.height, availableHeight);

    console.log(`Canvas resized - width: ${this.canvas.width}, height: ${this.canvas.height}, available height: ${availableHeight}, fullscreen: ${isFullScreen}`);

    // Redraw the image on the resized canvas
    this.ctx.drawImage(this.image, 0, 0, this.canvas.width, this.canvas.height);
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapePress(): void {
    this.zoomService.enableZoom();
    this.drawingService.setDrawingMode(false);
  }

  @HostListener('window:resize', ['$event'])
  onWindowResize(): void {
    if (this.canvas && this.image && this.image.complete) {
      // Small delay to ensure the browser has finished resizing
      setTimeout(() => {
        this.resizeCanvas();
        this.redrawCanvas();
      }, 50);
    }
  }

  @HostListener('document:mousedown', ['$event'])
  onMouseDown(event: MouseEvent): void {
    if (!this.drawingService.isDrawingMode()) return;

    this.isDrawing = true;
    const rect = this.canvasRef.nativeElement.getBoundingClientRect();
    const newScale = this.zoomLevel;

    this.startX = (event.clientX - rect.left) / newScale;
    this.startY = (event.clientY - rect.top) / newScale;

    // Initialize the current rectangle dimensions
    this.currentRectangle = { startX: this.startX, startY: this.startY, width: 0, height: 0, bruiteur:'', trust: Number(0),
      userName: this.userName, imageName: this.spectroName, isPersisted: false};

    // Track rectangle drawing start
    this.clickstreamService.trackRectangleDrawing('start', {
      startX: this.startX,
      startY: this.startY,
      coordinates: { x: event.clientX, y: event.clientY }
    });

    this.drawingService.setDrawingMode(false);
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    const rect = this.canvasRef.nativeElement.getBoundingClientRect();
    const newScale = this.zoomLevel;

    const cursorX = (event.clientX - rect.left) / newScale;
    const cursorY = (event.clientY - rect.top) / newScale;

    // Handle rectangle drawing if active
    if (this.isDrawing) {
      this.handleRectangleDrawing(cursorX, cursorY);
    }

    // Handle cursor calculations (frequency and time)
    this.handleCursorCalculations(cursorX, cursorY);

    const hoveredAiRectangle = this.isMouseOverAiRectangle(cursorX, cursorY);
    const hoveredRectangle = hoveredAiRectangle || this.isMouseOverRectangle(cursorX, cursorY);
    // console.log(hoveredRectangle)
    if (hoveredRectangle) {
      this.displayRectangleInfo(hoveredRectangle, hoveredAiRectangle !== null);
    } else {
      const infoElement = document.getElementById('rect-info')
      if (infoElement) {
        infoElement.style.display = 'none';
      }
    }
  }

  @HostListener('document:mouseup', ['$event'])
  onMouseUp(): void {
    if (!this.isDrawing) return;

    this.isDrawing = false;

    // Finalize the rectangle and store it
    if (this.currentRectangle) {
      this.currentRectangle.isPersisted = false;
      // this.rectangles.push({ ...this.currentRectangle }); // Save a copy of the current rectangle
      this.rectangleService.addRectangle({...this.currentRectangle});

      // Track rectangle drawing end
      this.clickstreamService.trackRectangleDrawing('end', {
        ...this.currentRectangle,
        width: this.currentRectangle.width,
        height: this.currentRectangle.height
      });
      this.rectangles = this.rectangleService.getRectangle();

      // this.printAllRectangles('Rectangles after drawing from mouseUp');
    }
    // Reset the current rectangle
    // Reset and show the annotation creator
    this.annotationService.setAnnotationCreatorVisibile(true); // Ensure proper re-rendering
    this.currentRectangle = null;

    this.zoomService.enableZoom();
  }

  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent): void {
    const rect = this.canvasRef.nativeElement.getBoundingClientRect();
    const newScale = this.zoomLevel;

    const clickX = (event.clientX - rect.left) / newScale;
    const clickY = (event.clientY - rect.top) / newScale;

    console.log(`clickX: ${clickX}, clickY: ${clickY}`);

    const aiRect = this.isMouseOverAiRectangle(clickX, clickY);
    if (aiRect) {
      //Track rectangle click for modification/deletion
      this.clickstreamService.trackAIRectangleClick(aiRect);
      this.openAnnotationCreator(aiRect);
      return;
    }

    const clickRect = this.isMouseOverRectangle(clickX, clickY);
    if (clickRect) {
      // Track rectangle click for modification/deletion
      this.clickstreamService.trackRectangleClick(clickRect);
      this.openAnnotationCreator(clickRect);
    }
  }

  private openAnnotationCreator(rectangle: Rectangle) {
    console.log('rectangle form openAnnotationCreator : ', rectangle);
    const lastRectangleIndex = this.rectangles.length - 1;
    this.currentRectangle = {...rectangle};

    // Check if this is an AI rectangle and track the click
    const isAiRectangle = this.rectangleService.getAiRectangles().includes(rectangle);
    if (isAiRectangle) {
      // Ensure the rectangle has AI suggestion markers
      this.currentRectangle.isAiSuggestion = true;
      this.currentRectangle.clickType = 'ai_rectangle_interaction';
      this.currentRectangle.suggestionType = 'ai_rectangle';

      this.clickstreamService.trackAIRectangleClick({
        rectangleData: this.currentRectangle,
        clickType: 'ai_rectangle_interaction',
        isAiSuggestion: true
      });

      console.log('🤖 AI Rectangle clicked - tracking enabled', this.currentRectangle);
    }

    // Start clickstream tracking for modification/deletion
    this.clickstreamService.startAnnotationForm(this.currentRectangle);

    this.annotationService.setAnnotationCreatorVisibile(true);
    console.log('This current from openAnnotationCreator: ',this.currentRectangle);
  }

  printAllRectangles(text: string): void {
    console.log(text, this.rectangles);
  }

  saveAnnotationCreator(annotation: Annotation): void {
    if (this.rectangles.length > 0) {
      const lastRectangleIndex = this.rectangles.length - 1;
      const lastRectangle = this.rectangles[lastRectangleIndex];

      if (lastRectangle.isPersisted === false) {
        lastRectangle.bruiteur = annotation.bruiteur;
        lastRectangle.trust = annotation.trust;

        console.log('lastRectangle : ', lastRectangle);
        console.log('current Rectangle saveAnnotationCreator:', this.currentRectangle);
        console.warn('Rectangle :', this.rectangleService.getRectangle());
      }

      const aiRectangle = this.rectangleService.getAiRectangles().includes(lastRectangle);
      console.log('aiRectangle : ', aiRectangle);

      if (aiRectangle) {
        console.warn('Into Airectangle : ');
        console.log('Before Removing AI Rectangle : ', this.rectangleService.getAiRectangles());
        console.log("Current Rectangle before adding :", this.currentRectangle);

        // AI suggestion acceptance is tracked in endAnnotationForm

        this.rectangleService.addRectangle({...this.currentRectangle!, isPersisted: true});
        this.rectangleService.removeAiRectangle(this.currentRectangle!);
        this.rectangles = this.rectangleService.getRectangle();

        console.log("Rectangles from save : ", this.rectangles);
        console.log('After Removing AI rectangle : ', this.rectangleService.getAiRectangles());
        console.log('User Rectangle Now :', this.rectangleService.getRectangle());

        console.log('Last rectangle :', this.rectangles[this.rectangles.length - 1]);

        this.addAnnotation();
        this.redrawCanvas();
      } else {
        console.warn('Rectangle was not saved 1 ');
        if (this.currentRectangle!.isPersisted === true && !aiRectangle) {
          // This is a modification - track it
          this.clickstreamService.endAnnotationForm('submitted', {
            bruiteur: annotation.bruiteur,
            trust: annotation.trust,
            action: 'modified'
          });

          console.error('Je suis la en error')
          const updateData = {
            image_name: this.spectroName,
            user_name: this.userName,
            temps_debut: this.currentRectangle!.tBottom,
            temps_fin: this.currentRectangle!.tTop,
            frequence_debut: this.currentRectangle!.fLeft,
            frequence_fin: this.currentRectangle!.fRight,
            new_type_buiteur: this.currentRectangle!.bruiteur,
            new_confiance: this.currentRectangle!.trust
          };

          this.annotationService.updateAnnotation(updateData).subscribe({
            next: (result) => {
              console.log('Rectangle updated', result);
              this.initializeCanvas();
            },
            error: (err) => console.log('Rectangle error', err),
          });
          console.warn('Rectangle is saved :', this.rectangleService.getRectangle())

        }
        else {
          console.error('Rectangle was not saved into else');

          // Track modification
          this.clickstreamService.endAnnotationForm('submitted', {
            bruiteur: annotation.bruiteur,
            trust: annotation.trust,
            action: 'created'
          });
          this.addAnnotation();
        }
      }
    }

    this.annotationService.setAnnotationCreatorVisibile(false);

    console.error('Current recangle after saving and after annotation form false :', this.currentRectangle)
    this.currentRectangle = null;
    this.redrawCanvas();
  }

  addAnnotation(): void {
    this.annotationService.addRectangles(this.rectangles[this.rectangles.length - 1]).subscribe({
      next: (response) => console.log('Rectangle saved:', response),
      error: (error) => console.error('Erro whenn saving rectangles :',error)
      });
    this.rectangles[this.rectangles.length - 1].isPersisted = true;

  }

  closeAnnotationCreator(): void {
    // Track form cancellation for modification/deletion
    this.clickstreamService.endAnnotationForm('cancelled');

    // Check if there are rectangles to remove
    if (this.rectangles.length > 0 && !this.rectangles[this.rectangles.length - 1].isPersisted) {
      console.log("Je supprime mon rectangle");
      // Remove the last rectangle from the list
      // this.rectangles.pop();
      this.rectangleService.removeLastRectangle();
    }
    // console.log('Rectangle removed : ', this.currentRectangle);
    if (this.currentRectangle && !this.currentRectangle.isPersisted) {
      this.currentRectangle = null;
    }
    // Hide the annotation creator form
    this.annotationService.setAnnotationCreatorVisibile(false);

    // Redraw the canvas to reflect the change
    this.redrawCanvas();
  }

  deleteAnnotation(): void {
    console.log('Deleting annotation');

    // Track annotation deletion
    this.clickstreamService.endAnnotationForm('deleted', this.currentRectangle);

    this.annotationService.deleteAnnotation(
      this.localStorage.getItem("spectroName")!,
      this.localStorage.getItem("uniqueId")!,
      this.currentRectangle!.tBottom!,
      this.currentRectangle!.tTop!,
      this.currentRectangle!.fLeft!,
      this.currentRectangle!.fRight!,

    ).subscribe(() => {
      // this.rectangles = this.rectangles.filter(rect => rect != this.currentRectangle);
      this.rectangleService.removeRectangle(this.currentRectangle!);

      console.log('Rectangle removed : ', this.currentRectangle);
      console.warn('Rectangle deleted : ', this.rectangleService.getRectangle());
      this.currentRectangle = null;
      this.annotationService.setAnnotationCreatorVisibile(false);

      this.redrawCanvas();
      this.initializeCanvas();
    })

  }


  private pixelToFrequency(xPixel: number, imageWidth: number, fMax: number): number {
    return fMax * (xPixel / imageWidth);
  }

  private pixelToTime(yPixel: number, imageHeight: number, tMax: number): number {
    return tMax * (1 - yPixel / imageHeight);
  }

  private logRectangleCorners(rect: Rectangle): {fLeft: number, fRight: number, tTop: number, tBottom: number} {
    const imageWidth = this.canvas.width;
    const imageHeight = this.canvas.height;
    const fMin = 0;
    const fMax = this.Maxfrequency;
    const tMin = 0;
    const tMax = this.maxTime;

    // Extract rectangle positions
    const topLeftX = rect.startX;
    const topRightX = rect.startX + rect.width;
    const topLeftY = rect.startY + rect.height;
    const bottomLeftY = rect.startY;

    // Convert time and frequency
    const fLeft = this.pixelToFrequency(topLeftX, imageWidth, fMax);
    const fRight = this.pixelToFrequency(topRightX, imageWidth, fMax);
    let tBottom = this.pixelToTime(bottomLeftY, imageHeight, tMax);
    let tTop = this.pixelToTime(topLeftY, imageHeight, tMax);

    if (tBottom > tTop) {
      [tBottom, tTop] = [tTop, tBottom];
    }

    // console.log(`Rectangle frequency Range: ${fLeft.toFixed(2)} Hz to ${fRight.toFixed(2)} Hz`);
    // console.log(`Rectangle time Range : ${tBottom.toFixed(2)} ms to ${tTop.toFixed(2)} ms`);

    return { fLeft, fRight, tTop, tBottom };
  }

  private handleRectangleDrawing(currentX: number, currentY: number): void {
    // Update the rectangle dimensions
    this.currentRectangle = {
      startX: this.startX,
      startY: this.startY,
      width: currentX - this.startX,
      height: currentY - this.startY,
      bruiteur: '',
      trust: Number(0),
      userName: this.userName,
      imageName: this.spectroName
    };

    const {fLeft, fRight, tTop, tBottom} = this.logRectangleCorners(this.currentRectangle);
    this.currentRectangle.fLeft = fLeft;
    this.currentRectangle.fRight = fRight;
    this.currentRectangle.tTop = tTop;
    this.currentRectangle.tBottom = tBottom;

    // console.log(`Rectangles after drawing handleRectangleDrawing : `, this.currentRectangle);

    // Redraw the canvas with the updated rectangle
    this.redrawCanvas();
    this.drawRectangle(this.currentRectangle);
  }


  private handleCursorCalculations(cursorX: number, cursorY: number): void {
    const cursorInfo = document.getElementById('cursor-info');
    if (cursorX >= 0 && cursorX <= this.canvas.width && cursorY >= 0 && cursorY <= this.canvas.height) {
      this.cursorX = cursorX;
      this.cursorY = cursorY;

      this.frequency = (this.Maxfrequency * this.cursorX) / this.canvas.width;
      this.time = Math.max(0, Math.min(this.maxTime * (1 - this.cursorY / this.canvas.height), this.maxTime));
      // const timeInMinutes = this.time / 60000;
      const timeInMinutes = this.millisToMinutesSeconds(this.time);

      // const cappedTimeInMinutes = Math.min(timeInMinutes, 60);

      if (cursorInfo) {
        cursorInfo.style.left = `${cursorX + 20}px`;
        cursorInfo.style.top = `${cursorY + 100}px`;
        cursorInfo.style.display = 'block';
        cursorInfo.innerHTML = `Frequency: ${this.frequency.toFixed(2)} Hz<br>Time: ${timeInMinutes} m`;
      }
    }
    else {
      this.resetCursorPosition();
      if (cursorInfo) {
        cursorInfo.style.display = 'none';
      }
    }
  }

  millisToMinutesSeconds(millis: number): string {
    const minutes = Math.floor(millis / 60000);
    const seconds = Math.floor((millis % 60000) / 1000);
    return `${minutes}.${seconds}`;

  }

  redrawCanvas(): void {
    // Clear the canvas and redraw the image
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.ctx.drawImage(this.image, 0, 0, this.canvas.width, this.canvas.height);

    const userRectangles: Rectangle[] = this.rectangleService.getRectangle();
    const aiRectangles: Rectangle[] = this.rectangleService.getAiRectangles();

    // console.log('All rectangle draws : ',allRectangles);

    // Redraw all saved rectangles
    aiRectangles.forEach(rect => {
      this.drawRectangle(rect, '#7DF9FF');

      // Track AI suggestion shown
      if(!(rect as any).tracked) {
        this.clickstreamService.trackAiSuggestionShown({
          rectangleData: rect,
          suggestionType: 'ai_rectangle',
          displayColor: '#7DF9FF'
        });
        // Mark as tracked to avoid duplicate tracking
        (rect as any).tracked = true;
      }
    });

    userRectangles.forEach(rect => {
      this.drawRectangle(rect, 'white');
    });


    console.log('aiRectangles redrawCanvas: ', aiRectangles)

    this.rectangles = [...userRectangles, ...aiRectangles];

    // Redraw the current rectangle if it exists
    console.log('currentRectangle redrawCanvas: ', this.currentRectangle?.isPersisted)

    if (this.currentRectangle && !this.currentRectangle.isPersisted) {
      console.log('test je suis dans le drawRectangle bizarre')
      this.drawRectangle(this.currentRectangle,'white');
    }
  }

  private frequencyToPixel(frequency: number, imageWidth: number, fMax: number): number {
    return (frequency / fMax) * imageWidth;
  }

  private timeToPixel(time: number, imageHeight: number,tMax: number): number {
    return imageHeight * (1 - time / tMax);
  }

  // redrawRectangle(color: string = '#7DF9FF'): void {
  //   rect.forEach(iRect => {
  //     const startX = this.frequencyToPixel(iRect.frequence_debut, this.canvas.width,0, this.Maxfrequency);
  //     const startY = this.timeToPixel(iRect.temps_debut, this.canvas.height,0, this.maxTime);
  //
  //     const endX = this.frequencyToPixel(iRect.frequence_fin, this.canvas.width,0, this.Maxfrequency);
  //     const endY = this.timeToPixel(iRect.temps_fin, this.canvas.height,0, this.maxTime);
  //
  //     const width = endX - startX;
  //     const height = endY - startY;
  //
  //     this.drawRectangle({startX, startY, width, height}, color)
  //   })
  //
  //   this.rectangles  = [...this.rectangles, ...convertedRctangles];
  //   console.log("Stored rectangles : ", this.rectangles.length);
  //
  //   this.redrawCanvas();
  // }

  private drawRectangle(rect: { startX: number; startY: number; width: number; height: number}, color: string = 'white'): void {
    const { startX, startY, width, height} = rect;
    this.ctx.beginPath();
    this.ctx.rect(startX, startY, width, height);
    this.ctx.strokeStyle = color;
    this.ctx.lineWidth = 1.25;
    this.ctx.stroke();
    this.ctx.closePath();
  }



  resetCursorPosition(): void {
    if (!this.zoomService.isZoomEnabled()) return; // Prevent resetting cursor when disabled

    this.cursorX = 0;
    this.cursorY = 0;
    this.frequency = 0;
    this.time = 0;

    const cursorInfo = document.getElementById('cursor-info');
    if (cursorInfo) {
      cursorInfo.style.display = 'none';
    }
  }

  private isMouseOverRectangle(x: number, y: number): Rectangle | null {
    const buffer = 5;
    // console.log(this.currentRectangle)
    return this.rectangles.find(rect =>
      x>= rect.startX - buffer &&
      x <= rect.startX + rect.width + buffer &&
      y >= rect.startY - buffer &&
      y <= rect.startY + rect.height +buffer
    ) || null;
    // const buffer = 3
    // for (const rect of this.rectangles) {
    //   if ( x>= rect.startX - buffer &&
    //     x <= rect.startX + rect.width + buffer &&
    //     y >= rect.startY - buffer &&
    //     y <= rect.startY + rect.height +buffer) {
    //     return rect
    //   }
    // }
    // return null;
  }

  private isMouseOverAiRectangle(x: number, y: number): Rectangle | null {
    const buffer = 5;
    const aiRectangles = this.rectangleService.getAiRectangles();
    return aiRectangles.find(rect =>
      x>= rect.startX - buffer &&
      x <= rect.startX + rect.width + buffer &&
      y >= rect.startY - buffer &&
      y <= rect.startY + rect.height +buffer
    ) || null;
  }

  private displayRectangleInfo(rect: Rectangle, isAi: boolean = false): void {
    if(!rect) {
      console.error('Rectangle not found');
    }
    const infoElement = document.getElementById('rect-info');
    if (infoElement) {
      infoElement.style.left = `${this.cursorX + 10}px`;
      infoElement.style.top = `${this.cursorY + 10}px`;
      infoElement.style.display = 'block';
      const aiLabel = isAi ? ' (IA)' : '';
      infoElement.innerHTML = `
  <p>Type de bruiteur: ${rect.bruiteur}${aiLabel}</p>
  <p>Confiance : ${rect.trust}${isAi ? '%' : ''}</p>
    `;
    }
  }

  handleSoundClick(event: MouseEvent): void {
    event.preventDefault();
    if (event.button !== 1) return; // Ensure it's the middle button

    // Only add a new position when explicitly requested
    if (this.middleClickPositions.length < 2) {
      this.middleClickPositions.push({x: event.clientX, y: event.clientY });
      console.log(`Click ${this.middleClickPositions.length}: X = ${event.clientX}, Y = ${event.clientY}`);
    }

    // If two clicks are captured, log them
    if (this.middleClickPositions.length === 2) {
      console.log('Two clicks captured:', this.middleClickPositions);
      this.playAudioBetweenBeats();
      this.isAudioPlayerVisible = true;
    }
  }

  playAudioBetweenBeats(): void {
    console.log('Playing audio between beats');

    // Convert X positions to a percentage of the screen width
    const canvasRect = this.canvasRef.nativeElement.getBoundingClientRect();
    const newScale = this.zoomLevel;

    const beat1 = (this.middleClickPositions[0].y - canvasRect.top) / newScale;
    const beat2 = (this.middleClickPositions[1].y - canvasRect.top) / newScale;

    const beatTime1 = this.maxTime * (1 - beat1 / this.canvas.height);
    const beatTime2 = this.maxTime * (1 - beat2 / this.canvas.height);

    this.startTime = beatTime1;
    this.endTime = beatTime2;

    if(this.startTime > this.endTime) {
      [this.startTime , this.endTime] = [this.endTime , this.startTime]
    }

    console.log(`Playing from ${this.millisToMinutesSeconds(beatTime1)}ms to ${this.millisToMinutesSeconds(beatTime2)}ms`);

    this.middleClickPositions = []; // Reset for next interaction
  }

  closePlayer() {
    this.isAudioPlayerVisible = false;
  }

}
