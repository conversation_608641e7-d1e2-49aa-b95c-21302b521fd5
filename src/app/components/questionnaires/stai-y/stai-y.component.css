.stai-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
  background-color: #f9f9f9;
  min-height: 100vh;
}

.stai-container h1 {
  color: #4a90e2;
  text-align: center;
  margin-bottom: 30px;
  font-size: 2.2em;
  font-weight: bold;
}

.stai-container > p {
  line-height: 1.6;
  margin-bottom: 20px;
  text-align: justify;
  font-size: 1.1em;
  color: #333;
  background-color: #f0f8ff;
  padding: 15px;
  border-left: 4px solid #4a90e2;
  border-radius: 5px;
}

form {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.participant-info {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.participant-info label {
  display: flex;
  flex-direction: column;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.participant-info select,
.participant-info input {
  margin-top: 8px;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.participant-info select:focus,
.participant-info input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
}

.questions {
  margin-bottom: 30px;
}

.question {
  margin-bottom: 25px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  transition: background-color 0.3s ease;
}

.question:hover {
  background-color: #f0f0f0;
}

.question p {
  font-weight: 500;
  font-size: 1.1em;
  margin-bottom: 15px;
  color: #333;
  line-height: 1.4;
}

.options {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  flex-wrap: wrap;
}

.options label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  flex: 1;
  min-width: 120px;
  padding: 10px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.options label:hover {
  background-color: #e8f4fd;
}

.options input[type="radio"] {
  display: none;
}

.radio-circle {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.options input[type="radio"]:checked + .radio-circle {
  border-color: #4a90e2;
  background-color: #4a90e2;
}

.options input[type="radio"]:checked + .radio-circle::after {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.options label:hover .radio-circle {
  border-color: #4a90e2;
  transform: scale(1.1);
}

.options label span {
  font-size: 0.9em;
  font-weight: 500;
  color: #666;
  text-align: center;
  line-height: 1.3;
}

.options input[type="radio"]:checked + .radio-circle + span {
  color: #4a90e2;
  font-weight: bold;
}

/* Button styling */
button {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 15px;
  min-width: 120px;
}

button[type="button"]:first-of-type {
  background-color: #6c757d;
  color: white;
}

button[type="button"]:first-of-type:hover {
  background-color: #5a6268;
}

button[type="button"]:last-of-type {
  background-color: #28a745;
  color: white;
}

button[type="button"]:last-of-type:hover {
  background-color: #218838;
}

/* Responsive design */
@media (max-width: 768px) {
  .stai-container {
    padding: 10px;
  }

  form {
    padding: 20px;
  }

  .participant-info {
    flex-direction: column;
    gap: 15px;
  }

  .options {
    flex-direction: column;
    gap: 10px;
  }

  .options label {
    flex-direction: row;
    justify-content: flex-start;
    min-width: auto;
    padding: 15px;
  }

  .radio-circle {
    margin-bottom: 0;
    margin-right: 15px;
  }

  .options label span {
    text-align: left;
  }

  button {
    width: 100%;
    margin-bottom: 10px;
    margin-right: 0;
  }
}
